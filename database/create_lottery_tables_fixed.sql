-- 抽奖系统数据库表创建脚本（修复版）
-- 创建时间: 2025-08-05
-- 描述: 将原本使用JSON文件存储的抽奖数据迁移到MySQL数据库
-- 数据库名称: tg_syl_data
-- 兼容MySQL 5.7+

-- 使用已创建的数据库
USE tg_syl_data;

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 1. 抽奖活动主表
CREATE TABLE IF NOT EXISTS lottery_activities (
    id VARCHAR(50) PRIMARY KEY COMMENT '活动ID',
    creator_id BIGINT NOT NULL COMMENT '创建者用户ID',
    name VARCHAR(255) NOT NULL COMMENT '活动名称',
    password VARCHAR(255) NOT NULL COMMENT '活动密码',
    draw_time DATETIME NOT NULL COMMENT '开奖时间',
    cover_photo TEXT COMMENT '封面图片URL',
    description TEXT COMMENT '活动描述',
    
    -- 奖品相关字段（向后兼容）
    prize_name VARCHAR(255) DEFAULT '神秘奖品' COMMENT '奖品名称（兼容字段）',
    prize_count INT DEFAULT 1 COMMENT '奖品数量（兼容字段）',
    winner_count INT DEFAULT 1 COMMENT '中奖人数',
    
    -- 状态字段
    status ENUM('creating', 'waiting', 'drawing', 'finished', 'cancelled') 
           DEFAULT 'creating' COMMENT '活动状态',
    
    -- 时间字段
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_creator_id (creator_id),
    INDEX idx_status (status),
    INDEX idx_draw_time (draw_time),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖活动表';

-- 2. 抽奖活动目标群组表（支持多群组）
CREATE TABLE IF NOT EXISTS lottery_target_groups (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    activity_id VARCHAR(50) NOT NULL COMMENT '活动ID',
    group_id BIGINT NOT NULL COMMENT '群组ID',
    group_name VARCHAR(255) COMMENT '群组名称',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (activity_id) REFERENCES lottery_activities(id) ON DELETE CASCADE,
    UNIQUE KEY uk_activity_group (activity_id, group_id),
    INDEX idx_group_id (group_id),
    INDEX idx_activity_id (activity_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖活动目标群组表';

-- 3. 奖品表
CREATE TABLE IF NOT EXISTS lottery_prizes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    activity_id VARCHAR(50) NOT NULL COMMENT '活动ID',
    name VARCHAR(255) NOT NULL COMMENT '奖品名称',
    count INT NOT NULL DEFAULT 1 COMMENT '奖品数量',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (activity_id) REFERENCES lottery_activities(id) ON DELETE CASCADE,
    INDEX idx_activity_id (activity_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖奖品表';

-- 4. 抽奖参与者表
CREATE TABLE IF NOT EXISTS lottery_participants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    activity_id VARCHAR(50) NOT NULL COMMENT '活动ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    username VARCHAR(255) COMMENT '用户名',
    first_name VARCHAR(255) COMMENT '名字',
    last_name VARCHAR(255) COMMENT '姓氏',
    join_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '参与时间',
    
    -- 中奖信息
    won_prize VARCHAR(255) COMMENT '获得的奖品名称',
    is_winner BOOLEAN DEFAULT FALSE COMMENT '是否中奖',
    win_time DATETIME NULL COMMENT '中奖时间',
    
    FOREIGN KEY (activity_id) REFERENCES lottery_activities(id) ON DELETE CASCADE,
    UNIQUE KEY uk_activity_user (activity_id, user_id),
    INDEX idx_user_id (user_id),
    INDEX idx_activity_id (activity_id),
    INDEX idx_join_time (join_time),
    INDEX idx_is_winner (is_winner)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖参与者表';

-- 5. 用户创建状态表（用于存储用户创建抽奖时的临时状态）
CREATE TABLE IF NOT EXISTS lottery_user_states (
    user_id BIGINT PRIMARY KEY COMMENT '用户ID',
    state_data TEXT COMMENT '状态数据（JSON格式字符串）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_update_time (update_time),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户创建状态表';

-- 6. 创建视图：活动详情视图（简化版，兼容性更好）
DROP VIEW IF EXISTS lottery_activity_details;
CREATE VIEW lottery_activity_details AS
SELECT 
    la.id,
    la.creator_id,
    la.name,
    la.password,
    la.draw_time,
    la.cover_photo,
    la.description,
    la.prize_name,
    la.prize_count,
    la.winner_count,
    la.status,
    la.create_time,
    la.update_time,
    
    -- 统计信息
    COALESCE(ps.total_participants, 0) as total_participants,
    COALESCE(ps.winner_count_actual, 0) as winner_count_actual,
    
    -- 时间相关计算
    CASE 
        WHEN la.draw_time > NOW() THEN TIMESTAMPDIFF(MINUTE, NOW(), la.draw_time)
        ELSE 0 
    END as minutes_to_draw,
    
    CASE 
        WHEN la.draw_time > NOW() THEN 'upcoming'
        WHEN la.status = 'finished' THEN 'completed'
        ELSE 'overdue'
    END as time_status
    
FROM lottery_activities la
LEFT JOIN (
    SELECT 
        activity_id,
        COUNT(*) as total_participants,
        SUM(CASE WHEN is_winner = 1 THEN 1 ELSE 0 END) as winner_count_actual
    FROM lottery_participants 
    GROUP BY activity_id
) ps ON la.id = ps.activity_id;

-- 7. 创建触发器：自动更新中奖状态
DROP TRIGGER IF EXISTS update_winner_status;
DELIMITER $$
CREATE TRIGGER update_winner_status 
BEFORE UPDATE ON lottery_participants
FOR EACH ROW
BEGIN
    -- 如果设置了中奖奖品，自动标记为中奖者并设置中奖时间
    IF NEW.won_prize IS NOT NULL AND NEW.won_prize != '' THEN
        SET NEW.is_winner = TRUE;
        IF OLD.won_prize IS NULL OR OLD.won_prize = '' THEN
            SET NEW.win_time = NOW();
        END IF;
    ELSEIF NEW.won_prize IS NULL OR NEW.won_prize = '' THEN
        SET NEW.is_winner = FALSE;
        SET NEW.win_time = NULL;
    END IF;
END$$
DELIMITER ;

-- 8. 创建存储过程：清理过期的用户状态数据
DROP PROCEDURE IF EXISTS CleanExpiredUserStates;
DELIMITER $$
CREATE PROCEDURE CleanExpiredUserStates(IN days_to_keep INT)
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    -- 设置默认值
    IF days_to_keep IS NULL OR days_to_keep <= 0 THEN
        SET days_to_keep = 7;
    END IF;
    
    -- 删除指定天数前的用户状态数据
    DELETE FROM lottery_user_states 
    WHERE update_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    SET deleted_count = ROW_COUNT();
    
    SELECT deleted_count as deleted_rows, 
           CONCAT('已清理 ', deleted_count, ' 条过期用户状态数据') as message;
END$$
DELIMITER ;

-- 9. 创建存储过程：获取活动完整信息
DROP PROCEDURE IF EXISTS GetActivityFullInfo;
DELIMITER $$
CREATE PROCEDURE GetActivityFullInfo(IN activity_id_param VARCHAR(50))
BEGIN
    -- 活动基本信息
    SELECT * FROM lottery_activity_details WHERE id = activity_id_param;
    
    -- 参与者列表
    SELECT 
        id,
        user_id,
        username,
        first_name,
        last_name,
        join_time,
        won_prize,
        is_winner,
        win_time
    FROM lottery_participants 
    WHERE activity_id = activity_id_param
    ORDER BY join_time;
    
    -- 目标群组列表
    SELECT 
        group_id,
        group_name
    FROM lottery_target_groups
    WHERE activity_id = activity_id_param;
    
    -- 奖品列表
    SELECT 
        name,
        count
    FROM lottery_prizes
    WHERE activity_id = activity_id_param;
END$$
DELIMITER ;

-- 10. 创建存储过程：获取活动统计信息
DROP PROCEDURE IF EXISTS GetActivityStats;
DELIMITER $$
CREATE PROCEDURE GetActivityStats(IN activity_id_param VARCHAR(50))
BEGIN
    SELECT 
        COUNT(*) as total_participants,
        SUM(CASE WHEN is_winner = 1 THEN 1 ELSE 0 END) as winner_count,
        MIN(join_time) as first_join_time,
        MAX(join_time) as last_join_time,
        COUNT(DISTINCT DATE(join_time)) as active_days
    FROM lottery_participants 
    WHERE activity_id = activity_id_param;
END$$
DELIMITER ;

-- 显示创建结果
SELECT '✅ 数据库表创建完成！' as status;

-- 显示所有表
SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '说明'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'tg_syl_data' 
AND TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

SELECT '🎉 抽奖系统数据库安装完成！' as message;
