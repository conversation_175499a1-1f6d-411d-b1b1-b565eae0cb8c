"""
MySQL数据库配置文件
用于抽奖系统的数据库连接配置
"""

from typing import Dict, Any

# 导入配置
try:
    from config import settings
except ImportError:
    # 如果无法导入config，则使用环境变量作为后备方案
    import os

    class FallbackSettings:
        MYSQL_HOST = os.getenv('MYSQL_HOST', 'localhost')
        MYSQL_PORT = int(os.getenv('MYSQL_PORT', '3306'))
        MYSQL_USER = os.getenv('MYSQL_USER', 'root')
        MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', '')
        MYSQL_DATABASE = os.getenv('MYSQL_DATABASE', 'tg_syl_data')
        MYSQL_CHARSET = 'utf8mb4'
        MYSQL_AUTOCOMMIT = False
        MYSQL_CONNECT_TIMEOUT = 10
        MYSQL_SQL_MODE = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
        MYSQL_TIME_ZONE = '+08:00'
        MYSQL_POOL_NAME = 'lottery_pool'
        MYSQL_POOL_SIZE = 10
        MYSQL_POOL_RESET_SESSION = True
        MYSQL_BUFFERED = True

    settings = FallbackSettings()

# MySQL数据库连接配置
MYSQL_CONFIG = {
    # 数据库主机地址
    'host': settings.MYSQL_HOST,

    # 数据库端口
    'port': settings.MYSQL_PORT,

    # 数据库用户名
    'user': settings.MYSQL_USER,

    # 数据库密码
    'password': settings.MYSQL_PASSWORD,

    # 数据库名称
    'database': settings.MYSQL_DATABASE,

    # 字符集
    'charset': settings.MYSQL_CHARSET,

    # 自动提交
    'autocommit': settings.MYSQL_AUTOCOMMIT,

    # 连接超时时间（秒）
    'connect_timeout': settings.MYSQL_CONNECT_TIMEOUT,

    # SQL模式
    'sql_mode': settings.MYSQL_SQL_MODE,

    # 时区设置
    'time_zone': settings.MYSQL_TIME_ZONE
}

# 连接池配置
MYSQL_POOL_CONFIG = {
    'pool_name': settings.MYSQL_POOL_NAME,
    'pool_size': settings.MYSQL_POOL_SIZE,
    'pool_reset_session': settings.MYSQL_POOL_RESET_SESSION,
    'buffered': settings.MYSQL_BUFFERED
}

def get_mysql_config() -> Dict[str, Any]:
    """
    获取MySQL配置
    
    Returns:
        Dict[str, Any]: MySQL连接配置
    """
    return MYSQL_CONFIG.copy()

def get_mysql_pool_config() -> Dict[str, Any]:
    """
    获取MySQL连接池配置
    
    Returns:
        Dict[str, Any]: MySQL连接池配置
    """
    config = MYSQL_CONFIG.copy()
    config.update(MYSQL_POOL_CONFIG)
    return config

# 配置说明
"""
数据库配置现在从 config.py 文件中获取。

你可以通过以下方式配置数据库连接：

1. 直接修改 config.py 文件中的数据库配置项
2. 设置环境变量（config.py 会自动读取）：
   MYSQL_HOST=localhost
   MYSQL_PORT=3306
   MYSQL_USER=lottery_user
   MYSQL_PASSWORD=your_secure_password
   MYSQL_DATABASE=tg_syl_data

注意：环境变量的优先级高于 config.py 中的默认值
"""
