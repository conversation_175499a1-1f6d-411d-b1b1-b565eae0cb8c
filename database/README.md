# 抽奖系统数据库迁移指南

本目录包含将抽奖系统从JSON文件存储迁移到MySQL数据库的相关脚本和配置文件。

## 文件说明

- `create_lottery_tables.sql` - MySQL数据库表创建脚本
- `migrate_json_to_mysql.py` - JSON数据迁移到MySQL的Python脚本
- `mysql_config.py` - MySQL数据库连接配置文件
- `README.md` - 本说明文件

## 数据库表结构

### 1. lottery_activities (抽奖活动主表)
- `id` - 活动ID (主键)
- `creator_id` - 创建者用户ID
- `name` - 活动名称
- `password` - 活动密码
- `draw_time` - 开奖时间
- `cover_photo` - 封面图片URL
- `description` - 活动描述
- `prize_name` - 奖品名称（兼容字段）
- `prize_count` - 奖品数量（兼容字段）
- `winner_count` - 中奖人数
- `status` - 活动状态
- `create_time` - 创建时间
- `update_time` - 更新时间

### 2. lottery_target_groups (目标群组表)
- `id` - 自增主键
- `activity_id` - 活动ID (外键)
- `group_id` - 群组ID
- `group_name` - 群组名称

### 3. lottery_prizes (奖品表)
- `id` - 自增主键
- `activity_id` - 活动ID (外键)
- `name` - 奖品名称
- `count` - 奖品数量

### 4. lottery_participants (参与者表)
- `id` - 自增主键
- `activity_id` - 活动ID (外键)
- `user_id` - 用户ID
- `username` - 用户名
- `first_name` - 名字
- `last_name` - 姓氏
- `join_time` - 参与时间
- `won_prize` - 获得的奖品名称
- `is_winner` - 是否中奖

### 5. lottery_user_states (用户状态表)
- `user_id` - 用户ID (主键)
- `state_data` - 状态数据 (JSON格式)
- `create_time` - 创建时间
- `update_time` - 更新时间

## 迁移步骤

### 1. 准备MySQL数据库

确保已安装MySQL服务器，并创建用于抽奖系统的数据库用户：

```sql
-- 创建数据库用户（可选）
CREATE USER 'lottery_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON tg_syl_data.* TO 'lottery_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 创建数据库表结构

执行SQL脚本创建表结构：

```bash
mysql -u root -p < create_lottery_tables.sql
```

或者在MySQL客户端中执行：

```sql
source /path/to/create_lottery_tables.sql;
```

### 3. 配置数据库连接

修改 `mysql_config.py` 文件中的数据库连接参数，或设置环境变量：

```bash
export MYSQL_HOST=localhost
export MYSQL_PORT=3306
export MYSQL_USER=lottery_user
export MYSQL_PASSWORD=your_secure_password
export MYSQL_DATABASE=tg_syl_data
```

### 4. 安装Python依赖

确保安装了必要的Python包：

```bash
pip install mysql-connector-python
```

### 5. 执行数据迁移

运行迁移脚本：

```bash
python migrate_json_to_mysql.py
```

脚本会：
1. 连接到MySQL数据库
2. 读取现有的JSON文件数据
3. 将数据转换并插入到MySQL表中
4. 验证迁移结果

### 6. 验证迁移结果

迁移完成后，可以通过以下SQL查询验证数据：

```sql
-- 查看活动数量
SELECT COUNT(*) FROM lottery_activities;

-- 查看活动详情（使用视图）
SELECT * FROM lottery_activity_details;

-- 查看参与者统计
SELECT 
    activity_id,
    COUNT(*) as total_participants,
    SUM(is_winner) as winner_count
FROM lottery_participants 
GROUP BY activity_id;
```

## 高级功能

### 存储过程

脚本创建了以下存储过程：

1. `CleanExpiredUserStates()` - 清理过期的用户状态数据
2. `GetActivityFullInfo(activity_id)` - 获取活动完整信息

使用示例：

```sql
-- 清理过期数据
CALL CleanExpiredUserStates();

-- 获取活动完整信息
CALL GetActivityFullInfo('your_activity_id');
```

### 视图

`lottery_activity_details` 视图提供了活动的完整信息，包括：
- 基本活动信息
- 参与者统计
- 目标群组列表（JSON格式）
- 奖品列表（JSON格式）

### 触发器

`update_winner_status` 触发器会在更新参与者的中奖奖品时自动设置中奖状态。

## 注意事项

1. **备份数据**: 在执行迁移前，请备份现有的JSON文件
2. **测试环境**: 建议先在测试环境中执行迁移
3. **权限检查**: 确保数据库用户有足够的权限
4. **字符集**: 使用utf8mb4字符集以支持emoji等特殊字符
5. **时区设置**: 注意时区设置，确保时间数据的正确性

## 故障排除

### 常见问题

1. **连接失败**: 检查MySQL服务是否运行，用户名密码是否正确
2. **权限不足**: 确保数据库用户有CREATE、INSERT、UPDATE、DELETE权限
3. **字符编码问题**: 确保数据库和表使用utf8mb4字符集
4. **时间格式问题**: 检查datetime字段的格式是否正确

### 日志查看

迁移脚本会输出详细的执行日志，包括：
- 连接状态
- 迁移进度
- 错误信息
- 最终统计

## 性能优化建议

1. **索引优化**: 根据查询需求添加适当的索引
2. **分区表**: 对于大量历史数据，考虑使用分区表
3. **连接池**: 在应用中使用连接池提高性能
4. **定期维护**: 定期清理过期数据和优化表结构

## 后续步骤

迁移完成后，需要：

1. 修改应用代码，将JSON文件操作替换为MySQL数据库操作
2. 更新配置文件，使用MySQL连接配置
3. 测试所有功能，确保迁移后系统正常运行
4. 监控数据库性能，根据需要进行优化
