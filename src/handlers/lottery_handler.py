"""
抽奖处理器

处理所有抽奖相关的命令和交互
"""

from datetime import datetime, timedelta
from typing import List, Tuple, Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, filters
from src.core.decorators import command_handler, message_handler, callback_query_handler
from src.core.lottery_manager import lottery_manager
from src.config.lottery_config import lottery_config
from src.models.lottery import LotteryStatus, LotteryActivity
from src.core.logger import default_logger as logger
import re


def parse_group_input(input_text: str) -> List[Tuple[int, str]]:
    """
    解析群组输入，支持群组链接和群组ID

    Args:
        input_text: 用户输入的群组信息，可以是链接或ID，用逗号分隔

    Returns:
        List[Tuple[int, str]]: [(group_id, group_name), ...]
    """
    groups = []
    items = [item.strip() for item in input_text.split(',') if item.strip()]

    for item in items:
        group_id = None
        group_name = item

        # 尝试解析Telegram群组链接
        # 格式: https://t.me/groupname 或 t.me/groupname
        link_pattern = r'(?:https?://)?(?:www\.)?t\.me/([a-zA-Z0-9_]+)'
        link_match = re.match(link_pattern, item)

        if link_match:
            # 群组链接，提取用户名
            username = link_match.group(1)
            group_name = f"@{username}"
            # 注意：这里我们无法直接获取群组ID，需要通过Bot API查询
            # 暂时使用用户名作为标识，后续需要实际查询
            group_id = f"@{username}"  # 临时使用用户名
        else:
            # 尝试解析为群组ID（负数）
            try:
                parsed_id = int(item)
                if parsed_id > 0:
                    parsed_id = -parsed_id  # 确保是负数
                group_id = str(parsed_id)  # 转换为字符串保持一致性
                group_name = f"群组{parsed_id}"
            except ValueError:
                # 可能是用户名格式 @groupname
                if item.startswith('@'):
                    group_id = item
                    group_name = item
                else:
                    # 无法解析，跳过
                    continue

        if group_id:
            groups.append((group_id, group_name))

    return groups


async def resolve_group_info(context: ContextTypes.DEFAULT_TYPE, group_identifier) -> Tuple[Optional[int], str]:
    """
    解析群组信息，获取真实的群组ID和名称

    Args:
        context: Telegram上下文
        group_identifier: 群组标识符（ID或用户名）

    Returns:
        Tuple[Optional[int], str]: (group_id, group_name)
    """
    try:
        if isinstance(group_identifier, int):
            # 已经是群组ID
            try:
                chat = await context.bot.get_chat(group_identifier)
                return group_identifier, chat.title or f"群组{group_identifier}"
            except:
                return group_identifier, f"群组{group_identifier}"

        elif isinstance(group_identifier, str):
            if group_identifier.startswith('@'):
                # 群组用户名，需要查询真实ID
                try:
                    chat = await context.bot.get_chat(group_identifier)
                    return chat.id, chat.title or group_identifier
                except Exception as e:
                    logger.warning(f"无法获取群组信息: {group_identifier}, 错误: {e}")
                    return None, group_identifier
            else:
                # 可能是字符串形式的群组ID
                try:
                    group_id = int(group_identifier)
                    chat = await context.bot.get_chat(group_id)
                    return group_id, chat.title or f"群组{group_id}"
                except ValueError:
                    # 不是数字，返回错误
                    logger.warning(f"无法解析群组标识符: {group_identifier}")
                    return None, str(group_identifier)
                except Exception as e:
                    # 网络错误等
                    logger.warning(f"无法获取群组信息: {group_identifier}, 错误: {e}")
                    try:
                        group_id = int(group_identifier)
                        return group_id, f"群组{group_id}"
                    except ValueError:
                        return None, str(group_identifier)

        else:
            return None, str(group_identifier)

    except Exception as e:
        logger.error(f"解析群组信息时出错: {e}")
        return None, str(group_identifier)


async def delete_message_by_id(context: ContextTypes.DEFAULT_TYPE, chat_id: int, message_id: int):
    """删除指定消息的辅助函数"""
    try:
        await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
        logger.debug(f"已删除消息: chat_id={chat_id}, message_id={message_id}")
    except Exception as e:
        logger.warning(f"删除消息失败: {e}")


# 创建步骤常量
STEP_NAME = "name"
STEP_COVER = "cover"
STEP_PRIZE = "prize"  # 新增奖品设置步骤
STEP_PASSWORD = "password"
STEP_TIME = "time"
STEP_GROUP = "group"
STEP_CONFIRM = "confirm"


@command_handler("create", "创建抽奖活动")
async def create_lottery(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """开始创建抽奖活动"""
    # 只允许私聊创建
    if update.message.chat.type != 'private':
        await update.message.reply_text("❌ 请在私聊中使用此命令创建抽奖活动")
        return
    
    user_id = update.message.from_user.id
    
    # 检查是否已有进行中的创建流程
    current_state = lottery_config.get_user_creation_state(user_id)
    if current_state:
        await update.message.reply_text(
            "⚠️ 您已有进行中的抽奖创建流程\n"
            "请先完成当前创建或使用 /cancel_create 取消"
        )
        return
    
    # 初始化创建状态
    creation_state = {
        'step': STEP_NAME,
        'data': {},
        'start_time': datetime.now().isoformat()
    }
    
    lottery_config.set_user_creation_state(user_id, creation_state)
    
    await update.message.reply_text(
        "🎉 **开始创建抽奖活动**\n\n"
        "请按照以下步骤设置您的抽奖活动：\n\n"
        "📝 **第1步：设置活动名称**\n"
        "请输入抽奖活动的名称（例如：新年红包抽奖）",
        parse_mode='Markdown'
    )


@command_handler("cancel_create", "取消创建抽奖")
async def cancel_create_lottery(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """取消创建抽奖活动"""
    if update.message.chat.type != 'private':
        return
    
    user_id = update.message.from_user.id
    current_state = lottery_config.get_user_creation_state(user_id)
    
    if not current_state:
        await update.message.reply_text("❌ 您没有进行中的抽奖创建流程")
        return
    
    lottery_config.clear_user_creation_state(user_id)
    await update.message.reply_text("✅ 已取消抽奖创建流程")


@message_handler(filters.TEXT & ~filters.COMMAND & filters.ChatType.PRIVATE, "处理创建流程消息", priority=10)
async def handle_creation_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理创建流程中的消息"""
    user_id = update.message.from_user.id
    current_state = lottery_config.get_user_creation_state(user_id)
    
    if not current_state:
        return
    
    step = current_state['step']
    text = update.message.text.strip()
    
    if step == STEP_NAME:
        await handle_name_step(update, context, current_state, text)
    elif step == STEP_COVER:
        await handle_cover_step(update, context, current_state, text)
    elif step == STEP_PRIZE:
        await handle_prize_step(update, context, current_state, text)
    elif step == STEP_PASSWORD:
        await handle_password_step(update, context, current_state, text)
    elif step == STEP_TIME:
        await handle_time_step(update, context, current_state, text)
    elif step == STEP_GROUP:
        await handle_group_step(update, context, current_state, text)


async def handle_name_step(update: Update, context: ContextTypes.DEFAULT_TYPE, 
                          current_state: dict, text: str):
    """处理名称设置步骤"""
    if len(text) > 50:
        await update.message.reply_text("❌ 活动名称不能超过50个字符，请重新输入")
        return
    
    current_state['data']['name'] = text
    current_state['step'] = STEP_COVER
    
    user_id = update.message.from_user.id
    lottery_config.set_user_creation_state(user_id, current_state)
    
    keyboard = [[InlineKeyboardButton("跳过封面设置", callback_data="skip_cover")]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await update.message.reply_text(
        f"✅ 活动名称已设置：{text}\n\n"
        "📸 **第2步：设置封面图片（可选）**\n"
        "请发送一张图片作为活动封面，或点击下方按钮跳过",
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )


async def handle_cover_step(update: Update, context: ContextTypes.DEFAULT_TYPE,
                           current_state: dict, text: str):
    """处理封面设置步骤"""
    # 如果是文本消息，提示用户发送图片或跳过
    await update.message.reply_text(
        "📸 请发送图片文件作为封面，或点击下方按钮跳过\n\n"
        "💡 提示：直接发送图片文件，不要发送文字",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton("跳过封面设置", callback_data="skip_cover")
        ]])
    )


async def handle_prize_step(update: Update, context: ContextTypes.DEFAULT_TYPE,
                           current_state: dict, text: str):
    """处理奖品设置步骤"""
    from src.models.lottery import Prize

    # 解析多个奖品，支持多行输入或分号分隔
    # 格式：奖品名称1,数量1;奖品名称2,数量2 或每行一个奖品
    prize_lines = []

    # 支持分号分隔或换行分隔
    if ';' in text:
        prize_lines = [line.strip() for line in text.split(';') if line.strip()]
    else:
        prize_lines = [line.strip() for line in text.split('\n') if line.strip()]

    # 如果只有一行，按逗号分隔检查是否是单个奖品
    if len(prize_lines) == 1 and ',' in prize_lines[0]:
        # 可能是单个奖品格式：奖品名称,数量
        single_line = prize_lines[0]
        parts = single_line.split(',')
        if len(parts) == 2:
            # 确实是单个奖品
            pass
        else:
            # 可能是多个奖品在一行，用逗号分隔，但格式错误
            await update.message.reply_text(
                "❌ 格式错误，请使用以下格式之一：\n\n"
                "**单个奖品：**\n"
                "`iPhone 15,1`\n\n"
                "**多个奖品（分号分隔）：**\n"
                "`iPhone 15,1;现金红包,3;京东卡,2`\n\n"
                "**多个奖品（换行分隔）：**\n"
                "```\niPhone 15,1\n现金红包,3\n京东卡,2```",
                parse_mode='Markdown'
            )
            return

    prizes = []
    total_winner_count = 0

    for line in prize_lines:
        parts = line.split(',', 1)

        if len(parts) != 2:
            await update.message.reply_text(
                f"❌ 奖品格式错误：`{line}`\n"
                "正确格式：`奖品名称,数量`\n"
                "例如：`iPhone 15,1`",
                parse_mode='Markdown'
            )
            return

        prize_name = parts[0].strip()

        if not prize_name:
            await update.message.reply_text("❌ 奖品名称不能为空")
            return

        try:
            prize_count = int(parts[1].strip())
            if prize_count <= 0:
                raise ValueError("奖品数量必须大于0")
        except ValueError:
            await update.message.reply_text(f"❌ 奖品数量必须是正整数：`{parts[1].strip()}`")
            return

        prizes.append(Prize(name=prize_name, count=prize_count))
        total_winner_count += prize_count

    if not prizes:
        await update.message.reply_text(
            "❌ 请至少设置一个奖品\n"
            "格式：`奖品名称,数量`",
            parse_mode='Markdown'
        )
        return

    # 保存奖品信息
    current_state['data']['prizes'] = [prize.to_dict() for prize in prizes]
    # 向后兼容：保存第一个奖品作为主奖品
    current_state['data']['prize_name'] = prizes[0].name
    current_state['data']['prize_count'] = prizes[0].count
    # 根据奖品总数设置中奖人数
    current_state['data']['winner_count'] = total_winner_count

    # 进入下一步
    current_state['step'] = STEP_PASSWORD

    user_id = update.message.from_user.id
    lottery_config.set_user_creation_state(user_id, current_state)

    # 生成奖品摘要
    prize_summary = ""
    for prize in prizes:
        prize_summary += f"• {prize.name} × {prize.count}\n"

    await update.message.reply_text(
        f"✅ 奖品已设置：\n{prize_summary}\n"
        f"🏆 中奖人数已自动设置为：{total_winner_count}\n\n"
        "🔑 **第4步：设置参与口令**\n"
        "请输入一个简短的口令，群组成员发送此口令即可参与抽奖\n\n"
        "💡 建议使用简单易记的词语，如：抽奖、福利、礼品等",
        parse_mode='Markdown'
    )


async def handle_password_step(update: Update, context: ContextTypes.DEFAULT_TYPE,
                              current_state: dict, text: str):
    """处理口令设置步骤"""
    if len(text) > 20:
        await update.message.reply_text("❌ 口令不能超过20个字符，请重新输入")
        return
    
    current_state['data']['password'] = text
    current_state['step'] = STEP_TIME
    
    user_id = update.message.from_user.id
    lottery_config.set_user_creation_state(user_id, current_state)
    
    await update.message.reply_text(
        f"✅ 抽奖口令已设置：{text}\n\n"
        "⏰ **第5步：设置开奖时间**\n"
        "请输入开奖时间，格式示例：\n"
        "• `2024-12-31 20:00` （年-月-日 时:分）\n"
        "• `明天 18:00`\n"
        "• `1小时后`\n"
        "• `30分钟后`",
        parse_mode='Markdown'
    )


async def handle_time_step(update: Update, context: ContextTypes.DEFAULT_TYPE,
                          current_state: dict, text: str):
    """处理时间设置步骤"""
    try:
        draw_time = parse_time_input(text)
        if draw_time <= datetime.now():
            await update.message.reply_text("❌ 开奖时间必须是未来时间，请重新输入")
            return
        
        current_state['data']['draw_time'] = draw_time.isoformat()
        current_state['step'] = STEP_GROUP
        
        user_id = update.message.from_user.id
        lottery_config.set_user_creation_state(user_id, current_state)
        
        await update.message.reply_text(
            f"✅ 开奖时间已设置：{draw_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            "🎯 **第6步：设置目标群组**\n"
            "请输入目标群组的链接或ID，支持多个群组（用逗号分隔）\n\n"
            "📝 **支持的格式：**\n"
            "• 群组链接：`https://t.me/groupname`\n"
            "• 群组用户名：`@groupname`\n"
            "• 群组ID：`-1001234567890`\n"
            "• 多个群组：`@group1, -1001234567890, https://t.me/group2`\n\n"
            "💡 输入完成后，您的抽奖活动就创建完成了！",
            parse_mode='Markdown'
        )
        
    except ValueError as e:
        await update.message.reply_text(f"❌ 时间格式错误：{str(e)}\n请重新输入")


async def handle_group_step(update: Update, context: ContextTypes.DEFAULT_TYPE,
                           current_state: dict, text: str):
    """处理群组设置步骤"""
    user_id = update.message.from_user.id

    try:
        # 解析群组输入
        group_inputs = parse_group_input(text)

        if not group_inputs:
            await update.message.reply_text(
                "❌ 无法解析群组信息，请检查输入格式\n\n"
                "📝 **支持的格式：**\n"
                "• 群组链接：`https://t.me/groupname`\n"
                "• 群组用户名：`@groupname`\n"
                "• 群组ID：`-1001234567890`\n"
                "• 多个群组：`@group1, -1001234567890, https://t.me/group2`",
                parse_mode='Markdown'
            )
            return

        # 解析群组信息
        target_group_ids = []
        target_group_names = []
        failed_groups = []

        await update.message.reply_text("🔍 正在验证群组信息，请稍候...")

        for group_identifier, initial_name in group_inputs:
            group_id, group_name = await resolve_group_info(context, group_identifier)

            if group_id:
                target_group_ids.append(group_id)
                target_group_names.append(group_name)
            else:
                failed_groups.append(initial_name)

        if not target_group_ids:
            await update.message.reply_text(
                "❌ 所有群组都无法验证，请检查：\n"
                "• 群组链接或ID是否正确\n"
                "• 机器人是否已加入这些群组\n"
                "• 群组是否为公开群组（私有群组需要使用群组ID）"
            )
            return

        # 完成抽奖创建
        data = current_state['data']
        draw_time = datetime.fromisoformat(data['draw_time'])

        # 处理奖品数据
        from src.models.lottery import Prize
        prizes = []
        if 'prizes' in data and data['prizes']:
            # 使用新的多奖品格式
            prizes = [Prize.from_dict(prize_data) for prize_data in data['prizes']]
        else:
            # 向后兼容：使用单一奖品格式
            prizes = [Prize(
                name=data.get('prize_name', '神秘奖品'),
                count=data.get('prize_count', 1)
            )]

        activity = lottery_manager.create_activity(
            creator_id=user_id,
            name=data['name'],
            password=data['password'],
            draw_time=draw_time,
            target_group_ids=target_group_ids,
            target_group_names=target_group_names,
            cover_photo=data.get('cover_photo'),
            description=data.get('description'),
            # 奖品相关参数
            prizes=prizes,
            prize_name=data.get('prize_name', '神秘奖品'),
            prize_count=data.get('prize_count', 1),
            winner_count=data.get('winner_count', 1)
        )

        # 清除创建状态
        lottery_config.clear_user_creation_state(user_id)

        # 发送成功消息
        success_text = f"🎉 **抽奖活动创建成功！**\n\n"
        success_text += f"🎯 活动名称: {activity.name}\n"

        # 显示奖品信息
        if activity.prizes and len(activity.prizes) > 1:
            success_text += f"🎁 奖品 ({len(activity.prizes)}种):\n"
            for prize in activity.prizes:
                success_text += f"   • {prize.name} × {prize.count}\n"
        else:
            success_text += f"🎁 奖品: {activity.prize_name} × {activity.prize_count}\n"

        success_text += f"🔑 参与口令: `{activity.password}`\n"
        success_text += f"⏰ 开奖时间: {activity.draw_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        success_text += f"🏆 获奖人数: {activity.winner_count}\n"
        success_text += f"🎯 目标群组 ({len(target_group_names)}个):\n"

        for i, name in enumerate(target_group_names, 1):
            success_text += f"   {i}. {name}\n"

        success_text += f"\n📢 **参与方式：**\n"
        success_text += f"在目标群组中发送口令 `{activity.password}` 即可参与抽奖！"

        if failed_groups:
            success_text += f"\n\n⚠️ **以下群组验证失败：**\n"
            for group in failed_groups:
                success_text += f"• {group}\n"

        # 如果有封面图片，发送带图片的消息
        if activity.cover_photo:
            await update.message.reply_photo(
                photo=activity.cover_photo,
                caption=success_text,
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text(success_text, parse_mode='Markdown')

        # 向所有目标群组发送抽奖通知并置顶
        group_notification_text = f"🎉 **新抽奖活动开始！**\n\n"
        group_notification_text += f"🎯 活动名称: {activity.name}\n"

        # 显示奖品信息
        if activity.prizes and len(activity.prizes) > 1:
            group_notification_text += f"🎁 奖品 ({len(activity.prizes)}种):\n"
            for prize in activity.prizes:
                group_notification_text += f"   • {prize.name} × {prize.count}\n"
        else:
            group_notification_text += f"🎁 奖品: {activity.prize_name} × {activity.prize_count}\n"

        group_notification_text += f"🔑 参与口令: `{activity.password}`\n"
        group_notification_text += f"⏰ 开奖时间: {activity.draw_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        group_notification_text += f"🏆 获奖人数: {activity.winner_count}\n\n"
        group_notification_text += f"📢 **参与方式：**\n"
        group_notification_text += f"在本群中发送口令 `{activity.password}` 即可参与抽奖！\n\n"
        group_notification_text += f"🎊 快来参与吧，好运等着你！"

        successful_groups = 0
        for group_id in target_group_ids:
            try:
                logger.info(f"向群组 {group_id} 发送抽奖通知")

                # 发送抽奖通知消息（显示封面）
                if activity.cover_photo:
                    logger.info(f"使用 send_photo 发送抽奖通知（带封面）到群组 {group_id}")
                    notification_msg = await context.bot.send_photo(
                        chat_id=group_id,
                        photo=activity.cover_photo,
                        caption=group_notification_text,
                        parse_mode='Markdown'
                    )
                else:
                    logger.info(f"使用 send_message 发送抽奖通知（无封面）到群组 {group_id}")
                    notification_msg = await context.bot.send_message(
                        chat_id=group_id,
                        text=group_notification_text,
                        parse_mode='Markdown'
                    )

                # 将抽奖通知消息置顶
                try:
                    await context.bot.pin_chat_message(
                        chat_id=group_id,
                        message_id=notification_msg.message_id,
                        disable_notification=False  # 发送通知
                    )
                    logger.info(f"抽奖通知消息已置顶: {notification_msg.message_id} (群组: {group_id})")
                    successful_groups += 1
                except Exception as pin_error:
                    logger.warning(f"置顶抽奖通知消息失败 (群组: {group_id}): {pin_error}")
                    successful_groups += 1  # 发送成功但置顶失败也算成功

            except Exception as e:
                logger.error(f"向群组 {group_id} 发送抽奖通知失败: {e}")

        # 私聊通知创建者
        try:
            private_text = f"✅ 抽奖活动已创建成功！\n\n"
            private_text += f"活动ID: `{activity.id}`\n"
            private_text += f"目标群组: {len(target_group_names)}个\n"
            private_text += f"通知发送: {successful_groups}/{len(target_group_ids)}个群组成功\n"
            private_text += f"您可以使用 /my_lotteries 查看您创建的所有抽奖活动"

            # 私聊通知也显示封面
            if activity.cover_photo:
                await context.bot.send_photo(
                    chat_id=user_id,
                    photo=activity.cover_photo,
                    caption=private_text,
                    parse_mode='Markdown'
                )
            else:
                await context.bot.send_message(
                    chat_id=user_id,
                    text=private_text,
                    parse_mode='Markdown'
                )
        except:
            pass  # 私聊通知失败不影响主流程

    except Exception as e:
        logger.error(f"处理群组设置时出错: {e}")
        await update.message.reply_text(f"❌ 创建抽奖活动时出错: {str(e)}")


@callback_query_handler("skip_cover", "跳过封面设置")
async def skip_cover_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """跳过封面设置"""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    current_state = lottery_config.get_user_creation_state(user_id)
    
    if not current_state or current_state['step'] != STEP_COVER:
        await query.edit_message_text("❌ 创建流程状态错误")
        return
    
    current_state['step'] = STEP_PRIZE
    lottery_config.set_user_creation_state(user_id, current_state)

    await query.edit_message_text(
        "✅ 已跳过封面设置\n\n"
        "🎁 **第3步：设置抽奖奖品**\n"
        "请输入奖品信息，支持设置多个奖品\n\n"
        "📝 **格式说明：**\n"
        "• 单个奖品：`奖品名称,数量`\n"
        "• 多个奖品（分号分隔）：`奖品1,数量1;奖品2,数量2`\n"
        "• 多个奖品（换行分隔）：每行一个奖品\n\n"
        "🎯 **示例：**\n"
        "```\niPhone 15,1\n现金红包,3\n京东卡,2```\n"
        "或：`iPhone 15,1;现金红包,3;京东卡,2`\n\n"
        "💡 中奖人数将根据奖品总数自动设置",
        parse_mode='Markdown'
    )


@command_handler("stop_lottery", "取消抽奖活动")
async def stop_lottery(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """取消抽奖活动"""
    # 只允许在私聊中使用
    if update.message.chat.type != 'private':
        await update.message.reply_text("❌ 此命令只能在私聊中使用")
        return

    user_id = update.message.from_user.id

    # 获取用户创建的正在进行中的抽奖活动
    active_activities = lottery_manager.get_user_active_activities(user_id)

    if not active_activities:
        await update.message.reply_text(
            "📭 您没有正在进行中的抽奖活动\n\n"
            "💡 只有状态为 '等待开奖' 的活动可以取消",
            parse_mode='Markdown'
        )
        return

    # 创建活动选择键盘
    keyboard = []
    for activity in active_activities:
        # 显示活动名称和状态
        status_text = "创建中" if activity.status == LotteryStatus.CREATING else "等待开奖"
        button_text = f"{activity.name} ({status_text})"
        callback_data = f"cancel_lottery:{activity.id}"
        keyboard.append([InlineKeyboardButton(button_text, callback_data=callback_data)])

    # 添加取消按钮
    keyboard.append([InlineKeyboardButton("❌ 取消", callback_data="cancel_lottery:cancel")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(
        f"🎯 **选择要取消的抽奖活动**\n\n"
        f"您有 {len(active_activities)} 个正在进行中的抽奖活动：\n\n"
        "⚠️ **注意：**\n"
        "• 取消后无法恢复\n"
        "• 已参与的用户将收到取消通知\n"
        "• 请谨慎操作",
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )


@command_handler("add_lottery_group", "添加抽奖群组（已废弃）")
async def add_lottery_group(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """旧的添加抽奖群组命令（已废弃）"""
    await update.message.reply_text(
        "📢 **此命令已更新！**\n\n"
        "现在创建抽奖时，您可以在私聊中直接输入群组链接或ID，支持多个群组。\n\n"
        "🎯 **新的创建方式：**\n"
        "1. 私聊机器人使用 `/create` 命令\n"
        "2. 按步骤设置抽奖信息\n"
        "3. 在最后一步输入群组信息（支持多个）\n\n"
        "📝 **支持的群组格式：**\n"
        "• 群组链接：`https://t.me/groupname`\n"
        "• 群组用户名：`@groupname`\n"
        "• 群组ID：`-1001234567890`\n"
        "• 多个群组：用逗号分隔",
        parse_mode='Markdown'
    )


def parse_time_input(time_str: str) -> datetime:
    """解析时间输入"""
    time_str = time_str.strip()
    now = datetime.now()
    
    # 处理相对时间
    if "小时后" in time_str:
        try:
            hours = int(time_str.replace("小时后", "").strip())
            return now + timedelta(hours=hours)
        except ValueError:
            pass
    
    if "分钟后" in time_str:
        try:
            minutes = int(time_str.replace("分钟后", "").strip())
            return now + timedelta(minutes=minutes)
        except ValueError:
            pass
    
    if "明天" in time_str:
        time_part = time_str.replace("明天", "").strip()
        if time_part:
            try:
                time_obj = datetime.strptime(time_part, "%H:%M").time()
                tomorrow = now.date() + timedelta(days=1)
                return datetime.combine(tomorrow, time_obj)
            except ValueError:
                pass
    
    # 处理绝对时间格式
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%m-%d %H:%M",
        "%d %H:%M"
    ]
    
    for fmt in formats:
        try:
            parsed_time = datetime.strptime(time_str, fmt)
            # 如果没有年份，使用当前年份
            if fmt.startswith("%m-") or fmt.startswith("%d"):
                parsed_time = parsed_time.replace(year=now.year)
                # 如果时间已过，使用明年
                if parsed_time <= now:
                    parsed_time = parsed_time.replace(year=now.year + 1)
            return parsed_time
        except ValueError:
            continue
    
    raise ValueError("无法解析时间格式，请使用正确的格式")


# 处理群组消息中的抽奖口令
@message_handler(filters.TEXT & ~filters.COMMAND & filters.ChatType.GROUPS, "处理抽奖口令", priority=10)
async def handle_lottery_password(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理群组中的抽奖口令"""
    message = update.message
    if not message or not message.text:
        return
    
    text = message.text.strip()
    group_id = message.chat.id
    user = message.from_user
    
    # 查找匹配的抽奖活动
    activity = lottery_manager.find_activity_by_password(text, group_id)
    if not activity:
        return  # 不是抽奖口令，让其他处理器继续处理

    # 找到匹配的抽奖活动，阻止其他处理器处理这条消息
    # 尝试参与抽奖
    success, msg = lottery_manager.join_lottery(
        activity.id, user.id, user.username, user.first_name, user.last_name
    )

    if success:
        # 参与成功，发送确认消息（不显示封面，10秒后删除）
        confirmation = f"🎉 {user.first_name or '用户'} 成功参与抽奖活动 **{activity.name}**！\n"
        confirmation += f"👥 当前参与人数: {activity.get_participant_count()}\n"
        confirmation += f"⏰ 开奖时间: {activity.draw_time.strftime('%Y-%m-%d %H:%M:%S')}"

        # 发送确认消息（不显示封面）
        confirmation_msg = await message.reply_text(confirmation, parse_mode='Markdown')

        # 10秒后删除确认消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, confirmation_msg.message_id
            ),
            when=10
        )
    else:
        # 参与失败，在群组中回复该成员（10秒后删除）
        user_name = user.first_name or '用户'

        # 根据不同的失败原因定制消息
        if "已经参与" in msg:
            error_text = f"❌ {user_name}，您已经参与过这个抽奖了，请勿重复参与！"
        elif "抽奖已结束" in msg:
            error_text = f"❌ {user_name}，抽奖活动已经结束了！"
        elif "抽奖已取消" in msg:
            error_text = f"❌ {user_name}，抽奖活动已被取消！"
        elif "时间已过" in msg:
            error_text = f"❌ {user_name}，抽奖时间已过，无法参与！"
        else:
            error_text = f"❌ {user_name}，{msg}"

        # 在群组中回复该成员
        error_msg = await message.reply_text(error_text, parse_mode='Markdown')

        # 10秒后删除错误消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, error_msg.message_id
            ),
            when=10
        )

    # 抛出异常阻止其他处理器继续处理这条消息
    from telegram.ext import ApplicationHandlerStop
    raise ApplicationHandlerStop


@command_handler("my_lotteries", "查看我的抽奖活动")
async def my_lotteries(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """查看用户创建的抽奖活动"""
    if update.message.chat.type != 'private':
        await update.message.reply_text("❌ 请在私聊中使用此命令")
        return

    user_id = update.message.from_user.id
    activities = lottery_manager.get_user_activities(user_id)

    if not activities:
        await update.message.reply_text("📝 您还没有创建任何抽奖活动")
        return

    # 按状态分组
    active_activities = [a for a in activities if a.status in [LotteryStatus.WAITING, LotteryStatus.DRAWING]]
    finished_activities = [a for a in activities if a.status == LotteryStatus.FINISHED]
    cancelled_activities = [a for a in activities if a.status == LotteryStatus.CANCELLED]

    text = f"🎯 **您的抽奖活动** (共{len(activities)}个)\n\n"

    if active_activities:
        text += "🟢 **进行中的活动:**\n"
        for activity in active_activities:
            status_emoji = "⏳" if activity.status == LotteryStatus.WAITING else "🎲"
            text += f"{status_emoji} {activity.name}\n"
            text += f"   👥 {len(activity.participants)}人参与 | ⏰ {activity.draw_time.strftime('%m-%d %H:%M')}\n"
            text += f"   ID: `{activity.id}`\n\n"

    if finished_activities:
        text += "✅ **已完成的活动:**\n"
        for activity in finished_activities[:5]:  # 只显示最近5个
            text += f"🏆 {activity.name}\n"
            text += f"   👥 {len(activity.participants)}人参与 | 🎉 {len(activity.winners)}人获奖\n\n"

    if cancelled_activities:
        text += f"❌ **已取消的活动:** {len(cancelled_activities)}个\n\n"

    text += "💡 使用 `/lottery_detail <活动ID>` 查看详细信息"

    await update.message.reply_text(text, parse_mode='Markdown')


@command_handler("lottery_detail", "查看抽奖活动详情")
async def lottery_detail(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """查看抽奖活动详情"""
    if not context.args:
        await update.message.reply_text("❌ 请提供活动ID\n用法: `/lottery_detail <活动ID>`", parse_mode='Markdown')
        return

    activity_id = context.args[0]
    activity = lottery_config.get_activity(activity_id)

    if not activity:
        await update.message.reply_text("❌ 找不到指定的抽奖活动")
        return

    user_id = update.message.from_user.id

    # 检查权限（创建者或群组成员可以查看）
    can_view = (activity.creator_id == user_id or
                update.message.chat.type in ['group', 'supergroup'] and
                update.message.chat.id == activity.target_group_id)

    if not can_view and update.message.chat.type == 'private':
        await update.message.reply_text("❌ 您没有权限查看此抽奖活动")
        return

    # 构建详情文本
    detail_text = activity.get_summary()

    if activity.participants:
        detail_text += f"\n👥 **参与者列表:**\n"
        for i, participant in enumerate(activity.participants[:10], 1):  # 最多显示10个
            detail_text += f"{i}. {participant.get_display_name()}\n"

        if len(activity.participants) > 10:
            detail_text += f"... 还有 {len(activity.participants) - 10} 人\n"

    if activity.winners:
        detail_text += f"\n🏆 **获奖者:**\n"
        for i, winner in enumerate(activity.winners, 1):
            detail_text += f"{i}. {winner.get_display_name()}\n"

    # 添加操作按钮（仅创建者可见）
    keyboard = []
    if activity.creator_id == user_id and update.message.chat.type == 'private':
        if activity.status == LotteryStatus.WAITING:
            keyboard.append([
                InlineKeyboardButton("🎲 立即开奖", callback_data=f"draw_now_{activity_id}"),
                InlineKeyboardButton("❌ 取消抽奖", callback_data=f"cancel_lottery:{activity_id}")
            ])
        elif activity.status == LotteryStatus.FINISHED and activity.winners:
            keyboard.append([
                InlineKeyboardButton("📊 查看统计", callback_data=f"lottery_stats_{activity_id}")
            ])

    reply_markup = InlineKeyboardMarkup(keyboard) if keyboard else None

    # 如果有封面图片，发送带图片的消息
    if activity.cover_photo:
        await update.message.reply_photo(
            photo=activity.cover_photo,
            caption=detail_text,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    else:
        await update.message.reply_text(detail_text, parse_mode='Markdown', reply_markup=reply_markup)


@callback_query_handler("cancel_lottery:", "取消抽奖选择")
async def cancel_lottery_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """取消抽奖回调处理"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    data = query.data.split(':', 1)[1]  # 获取活动ID或操作

    if data == "cancel":
        # 用户取消操作
        await query.edit_message_text("❌ 已取消操作")
        return

    activity_id = data

    # 获取活动信息
    activity = lottery_config.get_activity(activity_id)
    if not activity:
        await query.edit_message_text("❌ 抽奖活动不存在")
        return

    # 检查权限
    if activity.creator_id != user_id:
        await query.edit_message_text("❌ 只有抽奖创建者可以取消抽奖")
        return

    # 显示确认界面
    keyboard = [
        [
            InlineKeyboardButton("✅ 确认取消", callback_data=f"confirm_cancel:{activity_id}"),
            InlineKeyboardButton("🔙 返回", callback_data="return_cancel_list")
        ]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # 显示活动详情
    activity_info = f"🎯 **活动名称：** {activity.name}\n"
    activity_info += f"📅 **开奖时间：** {activity.draw_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
    activity_info += f"👥 **参与人数：** {len(activity.participants)}\n"

    # 显示奖品信息
    if activity.prizes and len(activity.prizes) > 1:
        activity_info += f"🎁 **奖品 ({len(activity.prizes)}种)：**\n"
        for prize in activity.prizes:
            activity_info += f"   • {prize.name} × {prize.count}\n"
    else:
        activity_info += f"🎁 **奖品：** {activity.prize_name} × {activity.prize_count}\n"

    activity_info += f"🎯 **目标群组：** {len(activity.target_group_names)}个\n"

    await query.edit_message_text(
        f"⚠️ **确认取消抽奖活动？**\n\n"
        f"{activity_info}\n"
        f"🚨 **警告：**\n"
        f"• 取消后无法恢复\n"
        f"• 已参与的 {len(activity.participants)} 位用户将收到取消通知\n"
        f"• 群组中的抽奖通知将被删除（如果可能）\n\n"
        f"请确认是否要取消此抽奖活动？",
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )


@callback_query_handler("confirm_cancel:", "确认取消抽奖")
async def confirm_cancel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """确认取消抽奖回调处理"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id
    activity_id = query.data.split(':', 1)[1]

    # 执行取消操作
    success, message = lottery_manager.cancel_activity(activity_id, user_id)

    if success:
        # 获取活动信息用于通知
        activity = lottery_config.get_activity(activity_id)

        await query.edit_message_text(
            f"✅ **抽奖活动已成功取消**\n\n"
            f"🎯 活动名称: {activity.name}\n"
            f"📅 原定开奖时间: {activity.draw_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"👥 已参与人数: {len(activity.participants)}\n\n"
            f"📢 系统将自动通知所有参与者抽奖已取消",
            parse_mode='Markdown'
        )

        # 发送取消通知到所有目标群组
        await send_cancellation_notification(context, activity)

    else:
        await query.edit_message_text(f"❌ 取消失败: {message}")


@callback_query_handler("return_cancel_list", "返回取消列表")
async def return_cancel_list_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """返回取消列表回调处理"""
    query = update.callback_query
    await query.answer()

    user_id = query.from_user.id

    # 重新获取活动列表
    active_activities = lottery_manager.get_user_active_activities(user_id)

    if not active_activities:
        await query.edit_message_text(
            "📭 您没有正在进行中的抽奖活动\n\n"
            "💡 只有状态为 '等待开奖' 的活动可以取消",
            parse_mode='Markdown'
        )
        return

    # 重新创建活动选择键盘
    keyboard = []
    for activity in active_activities:
        status_text = "创建中" if activity.status == LotteryStatus.CREATING else "等待开奖"
        button_text = f"{activity.name} ({status_text})"
        callback_data = f"cancel_lottery:{activity.id}"
        keyboard.append([InlineKeyboardButton(button_text, callback_data=callback_data)])

    keyboard.append([InlineKeyboardButton("❌ 取消", callback_data="cancel_lottery:cancel")])

    reply_markup = InlineKeyboardMarkup(keyboard)

    await query.edit_message_text(
        f"🎯 **选择要取消的抽奖活动**\n\n"
        f"您有 {len(active_activities)} 个正在进行中的抽奖活动：\n\n"
        "⚠️ **注意：**\n"
        "• 取消后无法恢复\n"
        "• 已参与的用户将收到取消通知\n"
        "• 请谨慎操作",
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )


async def send_cancellation_notification(context: ContextTypes.DEFAULT_TYPE, activity: LotteryActivity):
    """发送取消通知到所有目标群组"""
    cancellation_text = f"❌ **抽奖活动已取消**\n\n"
    cancellation_text += f"🎯 活动名称: {activity.name}\n"
    cancellation_text += f"📅 原定开奖时间: {activity.draw_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
    cancellation_text += f"👥 参与人数: {len(activity.participants)}\n\n"
    cancellation_text += f"😔 很抱歉，此抽奖活动已被创建者取消\n"
    cancellation_text += f"感谢大家的参与！"

    # 向所有目标群组发送取消通知
    for group_id in activity.target_group_ids:
        try:
            logger.info(f"向群组 {group_id} 发送抽奖取消通知")

            # 发送取消通知消息
            if activity.cover_photo:
                await context.bot.send_photo(
                    chat_id=group_id,
                    photo=activity.cover_photo,
                    caption=cancellation_text,
                    parse_mode='Markdown'
                )
            else:
                await context.bot.send_message(
                    chat_id=group_id,
                    text=cancellation_text,
                    parse_mode='Markdown'
                )

            logger.info(f"抽奖取消通知已发送到群组 {group_id}")

        except Exception as e:
            logger.error(f"向群组 {group_id} 发送取消通知失败: {e}")


@callback_query_handler("draw_now_", "立即开奖")
async def draw_now_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """立即开奖回调"""
    query = update.callback_query
    await query.answer()

    activity_id = query.data.replace("draw_now_", "")
    activity = lottery_config.get_activity(activity_id)

    if not activity:
        await query.edit_message_text("❌ 找不到指定的抽奖活动")
        return

    if activity.creator_id != query.from_user.id:
        await query.answer("❌ 您没有权限操作此抽奖", show_alert=True)
        return

    if activity.status != LotteryStatus.WAITING:
        await query.edit_message_text(f"❌ 抽奖状态错误: {activity.status.value}")
        return

    if len(activity.participants) == 0:
        await query.edit_message_text("❌ 没有参与者，无法开奖")
        return

    # 执行开奖
    success, message, winners = lottery_manager.draw_lottery(activity_id)

    if success:
        # 重新获取活动数据，确保获取到最新状态
        updated_activity = lottery_config.get_activity(activity_id)
        if not updated_activity:
            updated_activity = activity  # 如果获取失败，使用原来的数据

        result_text = f"🎉 **开奖完成！**\n\n"
        result_text += f"🎯 活动: {updated_activity.name}\n"

        # 显示奖品信息
        if updated_activity.prizes and len(updated_activity.prizes) > 1:
            result_text += f"🎁 奖品 ({len(updated_activity.prizes)}种):\n"
            for prize in updated_activity.prizes:
                result_text += f"   • {prize.name} × {prize.count}\n"
        else:
            result_text += f"🎁 奖品: {updated_activity.prize_name} × {updated_activity.prize_count}\n"

        result_text += f"👥 参与人数: {len(updated_activity.participants)}\n"
        result_text += f"🏆 获奖者:\n"

        for i, winner in enumerate(winners, 1):
            prize_info = f" - {winner.won_prize}" if winner.won_prize else ""
            result_text += f"{i}. {winner.get_display_name()}{prize_info}\n"

        # 发送结果到所有目标群组
        if updated_activity.target_group_ids:
            for group_id in updated_activity.target_group_ids:
                try:
                    # 调试日志：检查封面数据
                    logger.info(f"开奖结果发送 - 活动: {updated_activity.name}, 群组: {group_id}")
                    logger.info(f"封面数据: {repr(updated_activity.cover_photo)}")
                    logger.info(f"封面判断: {bool(updated_activity.cover_photo)}")

                    # 发送开奖结果消息（显示封面）
                    if updated_activity.cover_photo:
                        logger.info(f"使用 send_photo 发送开奖结果（带封面）到群组 {group_id}")
                        result_msg = await context.bot.send_photo(
                            chat_id=group_id,
                            photo=updated_activity.cover_photo,
                            caption=result_text,
                            parse_mode='Markdown'
                        )
                    else:
                        logger.info(f"使用 send_message 发送开奖结果（无封面）到群组 {group_id}")
                        result_msg = await context.bot.send_message(
                            chat_id=group_id,
                            text=result_text,
                            parse_mode='Markdown'
                        )

                    # 将开奖结果消息置顶
                    try:
                        await context.bot.pin_chat_message(
                            chat_id=group_id,
                            message_id=result_msg.message_id,
                            disable_notification=False  # 发送通知
                        )
                        logger.info(f"开奖结果消息已置顶: {result_msg.message_id} (群组: {group_id})")
                    except Exception as pin_error:
                        logger.warning(f"置顶开奖结果消息失败 (群组: {group_id}): {pin_error}")

                except Exception as e:
                    logger.error(f"发送开奖结果到群组 {group_id} 失败: {e}")

        await query.edit_message_text(result_text, parse_mode='Markdown')
    else:
        await query.edit_message_text(f"❌ 开奖失败: {message}")


@callback_query_handler("cancel_lottery_", "取消抽奖")
async def cancel_lottery_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """取消抽奖回调"""
    query = update.callback_query
    await query.answer()

    activity_id = query.data.replace("cancel_lottery_", "")
    success, message = lottery_manager.cancel_activity(activity_id, query.from_user.id)

    if success:
        await query.edit_message_text(f"✅ {message}")
    else:
        await query.edit_message_text(f"❌ {message}")


@command_handler("lottery_stats", "抽奖统计")
async def lottery_stats(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """查看抽奖统计"""
    if update.message.chat.type != 'private':
        await update.message.reply_text("❌ 请在私聊中使用此命令")
        return

    stats = lottery_config.get_statistics()

    stats_text = f"📊 **抽奖系统统计**\n\n"
    stats_text += f"🎯 总活动数: {stats['total_activities']}\n"
    stats_text += f"🟢 进行中: {stats['active_activities']}\n"
    stats_text += f"✅ 已完成: {stats['finished_activities']}\n"
    stats_text += f"👥 总参与人次: {stats['total_participants']}\n"

    await update.message.reply_text(stats_text, parse_mode='Markdown')


# 处理图片消息（用于封面设置）
@message_handler(filters.PHOTO & filters.ChatType.PRIVATE, "处理封面图片", priority=10)
async def handle_cover_photo(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理封面图片"""
    user_id = update.message.from_user.id
    current_state = lottery_config.get_user_creation_state(user_id)

    if not current_state or current_state['step'] != STEP_COVER:
        return

    # 获取最大尺寸的图片
    photo = update.message.photo[-1]
    file_id = photo.file_id

    current_state['data']['cover_photo'] = file_id
    current_state['step'] = STEP_PRIZE

    lottery_config.set_user_creation_state(user_id, current_state)

    await update.message.reply_text(
        "✅ 封面图片已设置\n\n"
        "🎁 **第3步：设置抽奖奖品**\n"
        "请输入奖品信息，支持设置多个奖品\n\n"
        "📝 **格式说明：**\n"
        "• 单个奖品：`奖品名称,数量`\n"
        "• 多个奖品（分号分隔）：`奖品1,数量1;奖品2,数量2`\n"
        "• 多个奖品（换行分隔）：每行一个奖品\n\n"
        "🎯 **示例：**\n"
        "```\niPhone 15,1\n现金红包,3\n京东卡,2```\n"
        "或：`iPhone 15,1;现金红包,3;京东卡,2`\n\n"
        "💡 中奖人数将根据奖品总数自动设置",
        parse_mode='Markdown'
    )
