"""
抽奖数据模型

定义抽奖活动相关的数据结构
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
import json
import random


@dataclass
class Prize:
    """奖品数据结构"""
    name: str  # 奖品名称
    count: int  # 奖品数量

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'count': self.count
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Prize':
        """从字典创建实例"""
        return cls(
            name=data['name'],
            count=data['count']
        )


class LotteryStatus(Enum):
    """抽奖状态枚举"""
    CREATING = "creating"      # 创建中
    WAITING = "waiting"        # 等待开奖
    DRAWING = "drawing"        # 开奖中
    FINISHED = "finished"      # 已完成
    CANCELLED = "cancelled"    # 已取消


@dataclass
class LotteryParticipant:
    """抽奖参与者"""
    user_id: int
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    join_time: datetime = field(default_factory=datetime.now)
    # 中奖信息
    won_prize: Optional[str] = None  # 获得的奖品名称
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'join_time': self.join_time.isoformat(),
            'won_prize': self.won_prize
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LotteryParticipant':
        """从字典创建实例"""
        return cls(
            user_id=data['user_id'],
            username=data.get('username'),
            first_name=data.get('first_name'),
            last_name=data.get('last_name'),
            join_time=datetime.fromisoformat(data['join_time']),
            won_prize=data.get('won_prize')
        )
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        if self.username:
            return f"@{self.username}"
        elif self.first_name:
            name = self.first_name
            if self.last_name:
                name += f" {self.last_name}"
            return name
        else:
            return f"用户{self.user_id}"


@dataclass
class LotteryActivity:
    """抽奖活动"""
    id: str
    creator_id: int
    name: str
    password: str
    draw_time: datetime
    target_group_ids: List[int] = field(default_factory=list)  # 支持多个群组
    target_group_names: List[str] = field(default_factory=list)  # 对应的群组名称
    cover_photo: Optional[str] = None
    description: Optional[str] = None
    # 奖品相关字段
    prizes: List[Prize] = field(default_factory=list)  # 奖品列表
    # 向后兼容字段
    prize_name: str = "神秘奖品"  # 奖品名称（兼容）
    prize_count: int = 1  # 奖品数量（兼容）
    winner_count: int = 1  # 中奖人数（根据奖品数量设置）
    status: LotteryStatus = LotteryStatus.CREATING
    participants: List[LotteryParticipant] = field(default_factory=list)
    winners: List[LotteryParticipant] = field(default_factory=list)
    create_time: datetime = field(default_factory=datetime.now)

    # 兼容性属性，保持向后兼容
    @property
    def target_group_id(self) -> Optional[int]:
        """获取第一个群组ID（向后兼容）"""
        return self.target_group_ids[0] if self.target_group_ids else None

    @property
    def target_group_name(self) -> Optional[str]:
        """获取第一个群组名称（向后兼容）"""
        return self.target_group_names[0] if self.target_group_names else None

    def get_total_prize_count(self) -> int:
        """获取奖品总数"""
        if self.prizes:
            return sum(prize.count for prize in self.prizes)
        return self.prize_count

    def create_prize_pool(self) -> List[str]:
        """创建奖品池（用于随机分配）"""
        prize_pool = []

        if self.prizes:
            # 使用新的奖品列表
            for prize in self.prizes:
                prize_pool.extend([prize.name] * prize.count)
        else:
            # 向后兼容：使用旧的单一奖品
            prize_pool.extend([self.prize_name] * self.prize_count)

        return prize_pool

    def assign_random_prizes(self, winners: List['LotteryParticipant']) -> None:
        """随机分配奖品给中奖者"""
        if not winners:
            return

        # 创建奖品池
        prize_pool = self.create_prize_pool()

        # 随机打乱奖品池
        random.shuffle(prize_pool)

        # 为每个中奖者分配奖品
        for i, winner in enumerate(winners):
            if i < len(prize_pool):
                winner.won_prize = prize_pool[i]
            else:
                # 如果奖品不够，分配默认奖品
                winner.won_prize = self.prize_name if self.prize_name else "神秘奖品"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'creator_id': self.creator_id,
            'name': self.name,
            'password': self.password,
            'draw_time': self.draw_time.isoformat(),
            'target_group_ids': self.target_group_ids,
            'target_group_names': self.target_group_names,
            # 保持向后兼容
            'target_group_id': self.target_group_id,
            'target_group_name': self.target_group_name,
            'cover_photo': self.cover_photo,
            'description': self.description,
            # 奖品相关字段
            'prizes': [prize.to_dict() for prize in self.prizes],
            'prize_name': self.prize_name,
            'prize_count': self.prize_count,
            'winner_count': self.winner_count,
            'status': self.status.value,
            'participants': [p.to_dict() for p in self.participants],
            'winners': [w.to_dict() for w in self.winners],
            'create_time': self.create_time.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LotteryActivity':
        """从字典创建实例"""
        # 处理新格式和旧格式的兼容性
        target_group_ids = data.get('target_group_ids', [])
        target_group_names = data.get('target_group_names', [])

        # 如果是旧格式，转换为新格式
        if not target_group_ids and data.get('target_group_id'):
            target_group_ids = [data['target_group_id']]
            target_group_names = [data.get('target_group_name', '')]

        # 处理奖品列表
        prizes = []
        if 'prizes' in data and data['prizes']:
            prizes = [Prize.from_dict(p) for p in data['prizes']]
        elif data.get('prize_name') and data.get('prize_count'):
            # 向后兼容：从旧格式创建奖品列表
            prizes = [Prize(name=data['prize_name'], count=data['prize_count'])]

        return cls(
            id=data['id'],
            creator_id=data['creator_id'],
            name=data['name'],
            password=data['password'],
            draw_time=datetime.fromisoformat(data['draw_time']),
            target_group_ids=target_group_ids,
            target_group_names=target_group_names,
            cover_photo=data.get('cover_photo'),
            description=data.get('description'),
            # 奖品相关字段
            prizes=prizes,
            prize_name=data.get('prize_name', '神秘奖品'),
            prize_count=data.get('prize_count', 1),
            winner_count=data.get('winner_count', 1),
            status=LotteryStatus(data.get('status', 'creating')),
            participants=[LotteryParticipant.from_dict(p) for p in data.get('participants', [])],
            winners=[LotteryParticipant.from_dict(w) for w in data.get('winners', [])],
            create_time=datetime.fromisoformat(data['create_time'])
        )
    
    def add_participant(self, user_id: int, username: str = None, 
                       first_name: str = None, last_name: str = None) -> bool:
        """添加参与者"""
        # 检查是否已经参与
        if any(p.user_id == user_id for p in self.participants):
            return False
        
        participant = LotteryParticipant(
            user_id=user_id,
            username=username,
            first_name=first_name,
            last_name=last_name
        )
        self.participants.append(participant)
        return True
    
    def remove_participant(self, user_id: int) -> bool:
        """移除参与者"""
        for i, participant in enumerate(self.participants):
            if participant.user_id == user_id:
                del self.participants[i]
                return True
        return False
    
    def get_participant_count(self) -> int:
        """获取参与者数量"""
        return len(self.participants)
    
    def is_participant(self, user_id: int) -> bool:
        """检查用户是否已参与"""
        return any(p.user_id == user_id for p in self.participants)
    
    def can_participate(self) -> bool:
        """检查是否可以参与"""
        return self.status == LotteryStatus.WAITING and datetime.now() < self.draw_time
    
    def is_ready_to_draw(self) -> bool:
        """检查是否准备开奖"""
        return (self.status == LotteryStatus.WAITING and 
                datetime.now() >= self.draw_time and 
                len(self.participants) > 0)
    
    def get_summary(self) -> str:
        """获取活动摘要"""
        status_text = {
            LotteryStatus.CREATING: "创建中",
            LotteryStatus.WAITING: "等待开奖",
            LotteryStatus.DRAWING: "开奖中",
            LotteryStatus.FINISHED: "已完成",
            LotteryStatus.CANCELLED: "已取消"
        }

        summary = f"🎉 **{self.name}**\n"

        # 显示奖品信息
        if self.prizes and len(self.prizes) > 1:
            # 多个奖品
            summary += f"🎁 奖品 ({len(self.prizes)}种):\n"
            for prize in self.prizes:
                summary += f"   • {prize.name} × {prize.count}\n"
        elif self.prizes and len(self.prizes) == 1:
            # 单个奖品
            prize = self.prizes[0]
            summary += f"🎁 奖品: {prize.name} × {prize.count}\n"
        else:
            # 向后兼容
            summary += f"🎁 奖品: {self.prize_name} × {self.prize_count}\n"

        summary += f"📅 开奖时间: {self.draw_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        summary += f"👥 参与人数: {len(self.participants)}\n"
        summary += f"🏆 获奖人数: {self.winner_count}\n"
        summary += f"📊 状态: {status_text.get(self.status, '未知')}\n"

        # 显示多个目标群组
        if self.target_group_names:
            if len(self.target_group_names) == 1:
                summary += f"🎯 目标群组: {self.target_group_names[0]}\n"
            else:
                summary += f"🎯 目标群组 ({len(self.target_group_names)}个):\n"
                for i, name in enumerate(self.target_group_names, 1):
                    summary += f"   {i}. {name}\n"

        if self.description:
            summary += f"📝 描述: {self.description}\n"

        return summary
