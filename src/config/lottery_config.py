"""
抽奖配置管理模块

管理抽奖活动数据的存储和读取
"""

import json
from typing import List, Dict, Optional
from pathlib import Path
from datetime import datetime
from src.core.logger import default_logger as logger
from src.models.lottery import LotteryActivity, LotteryStatus


class LotteryConfig:
    """抽奖配置管理器"""
    
    def __init__(self, config_dir: str = "data"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.lottery_file = self.config_dir / "lottery_data.json"
        self.user_states_file = self.config_dir / "lottery_user_states.json"
        
        # 内存中的数据
        self.activities: Dict[str, LotteryActivity] = {}
        self.user_creation_states: Dict[str, Dict] = {}  # 用户创建状态
        
        # 加载数据
        self.load_data()
    
    def load_data(self):
        """加载抽奖数据"""
        try:
            # 加载抽奖活动数据
            if self.lottery_file.exists():
                with open(self.lottery_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for activity_id, activity_data in data.get('activities', {}).items():
                        self.activities[activity_id] = LotteryActivity.from_dict(activity_data)
            
            # 加载用户创建状态
            if self.user_states_file.exists():
                with open(self.user_states_file, 'r', encoding='utf-8') as f:
                    self.user_creation_states = json.load(f)
            
            logger.info(f"抽奖数据加载完成: {len(self.activities)}个活动")
            
        except Exception as e:
            logger.error(f"加载抽奖数据时出错: {e}")
            self.activities = {}
            self.user_creation_states = {}
    
    def save_data(self):
        """保存抽奖数据"""
        try:
            # 保存抽奖活动数据
            data = {
                'activities': {
                    activity_id: activity.to_dict() 
                    for activity_id, activity in self.activities.items()
                },
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.lottery_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 保存用户创建状态
            with open(self.user_states_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_creation_states, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存抽奖数据时出错: {e}")
    
    def create_activity(self, activity: LotteryActivity) -> bool:
        """创建抽奖活动"""
        try:
            self.activities[activity.id] = activity
            self.save_data()
            logger.info(f"创建抽奖活动: {activity.id} - {activity.name}")
            return True
        except Exception as e:
            logger.error(f"创建抽奖活动时出错: {e}")
            return False
    
    def get_activity(self, activity_id: str) -> Optional[LotteryActivity]:
        """获取抽奖活动"""
        return self.activities.get(activity_id)
    
    def update_activity(self, activity: LotteryActivity) -> bool:
        """更新抽奖活动"""
        try:
            if activity.id in self.activities:
                self.activities[activity.id] = activity
                self.save_data()
                return True
            return False
        except Exception as e:
            logger.error(f"更新抽奖活动时出错: {e}")
            return False
    
    def delete_activity(self, activity_id: str) -> bool:
        """删除抽奖活动"""
        try:
            if activity_id in self.activities:
                del self.activities[activity_id]
                self.save_data()
                logger.info(f"删除抽奖活动: {activity_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"删除抽奖活动时出错: {e}")
            return False
    
    def get_activities_by_creator(self, creator_id: int) -> List[LotteryActivity]:
        """获取用户创建的抽奖活动"""
        return [activity for activity in self.activities.values() 
                if activity.creator_id == creator_id]
    
    def get_activities_by_group(self, group_id: int) -> List[LotteryActivity]:
        """获取群组的抽奖活动"""
        return [activity for activity in self.activities.values()
                if group_id in activity.target_group_ids]
    
    def get_active_activities(self) -> List[LotteryActivity]:
        """获取活跃的抽奖活动"""
        return [activity for activity in self.activities.values() 
                if activity.status in [LotteryStatus.WAITING, LotteryStatus.DRAWING]]
    
    def get_ready_to_draw_activities(self) -> List[LotteryActivity]:
        """获取准备开奖的活动"""
        return [activity for activity in self.activities.values() 
                if activity.is_ready_to_draw()]
    
    def find_activity_by_password(self, password: str, group_id: int) -> Optional[LotteryActivity]:
        """通过口令和群组ID查找活动"""
        for activity in self.activities.values():
            if (activity.password == password and
                group_id in activity.target_group_ids and
                activity.can_participate()):
                return activity
        return None
    
    # 用户创建状态管理
    def set_user_creation_state(self, user_id: int, state: Dict):
        """设置用户创建状态"""
        self.user_creation_states[str(user_id)] = state
        self.save_data()
    
    def get_user_creation_state(self, user_id: int) -> Optional[Dict]:
        """获取用户创建状态"""
        return self.user_creation_states.get(str(user_id))
    
    def clear_user_creation_state(self, user_id: int):
        """清除用户创建状态"""
        user_key = str(user_id)
        if user_key in self.user_creation_states:
            del self.user_creation_states[user_key]
            self.save_data()
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        total_activities = len(self.activities)
        active_activities = len(self.get_active_activities())
        finished_activities = len([a for a in self.activities.values() 
                                 if a.status == LotteryStatus.FINISHED])
        total_participants = sum(len(a.participants) for a in self.activities.values())
        
        return {
            'total_activities': total_activities,
            'active_activities': active_activities,
            'finished_activities': finished_activities,
            'total_participants': total_participants
        }


# 全局配置实例
lottery_config = LotteryConfig()
